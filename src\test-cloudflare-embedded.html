<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cloudflare Turnstile 内嵌模式测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .button {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        margin: 10px 5px;
      }
      .button:hover {
        background: #0056b3;
      }
      .button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .log {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin-top: 20px;
        max-height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 14px;
      }
      .log-entry {
        margin-bottom: 5px;
        padding: 2px 0;
      }
      .log-success {
        color: #28a745;
      }
      .log-error {
        color: #dc3545;
      }
      .log-warning {
        color: #ffc107;
      }
      .log-info {
        color: #17a2b8;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Cloudflare Turnstile 内嵌模式测试</h1>
      <p>这个页面用于测试 CloudflareMgr 的内嵌 iframe 模式功能。</p>

      <div>
        <button id="testEmbedded" class="button">测试内嵌模式验证</button>
        <button id="testBasic" class="button">测试基础模式验证</button>
        <button id="clearLogs" class="button">清除日志</button>
      </div>

      <div id="turnstile-container" style="margin: 20px 0"></div>

      <div class="log" id="logContainer">
        <div class="log-entry log-info">等待测试...</div>
      </div>
    </div>

    <script type="module">
      // 模拟 CloudflareMgr 的基本结构用于测试
      const CF_TURNSTILE_TYPE = {
        LOGIN_SUBMIT: "SCENE_LOGIN",
        KYC_SUBMIT: "SCENE_SUB_KYC_INFO",
      };

      // 日志功能
      function addLog(type, message, data = null) {
        const logContainer = document.getElementById("logContainer");
        const time = new Date().toLocaleTimeString();
        const logEntry = document.createElement("div");
        logEntry.className = `log-entry log-${type}`;
        logEntry.innerHTML = `[${time}] ${message}`;
        if (data) {
          logEntry.innerHTML += ` - ${JSON.stringify(data)}`;
        }
        logContainer.insertBefore(logEntry, logContainer.firstChild);

        // 限制日志数量
        const entries = logContainer.querySelectorAll(".log-entry");
        if (entries.length > 50) {
          entries[entries.length - 1].remove();
        }
      }

      // 模拟内嵌模式测试
      function testEmbeddedMode() {
        addLog("info", "开始测试内嵌模式");

        // 模拟创建内嵌 iframe
        const overlay = document.createElement("div");
        overlay.id = "turnstile-iframe-overlay";
        overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 9999;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

        const container = document.createElement("div");
        container.style.cssText = `
                position: relative;
                width: 400px;
                height: 600px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                overflow: hidden;
            `;

        const closeButton = document.createElement("button");
        closeButton.innerHTML = "×";
        closeButton.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                width: 30px;
                height: 30px;
                border: none;
                background: rgba(0, 0, 0, 0.1);
                color: #666;
                font-size: 20px;
                font-weight: bold;
                border-radius: 50%;
                cursor: pointer;
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

        // 创建模拟内容而不是真实的 iframe
        const content = document.createElement("div");
        content.style.cssText = `
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-align: center;
                padding: 20px;
                box-sizing: border-box;
            `;
        content.innerHTML = `
                <h2>🔒 Cloudflare Turnstile</h2>
                <p>内嵌模式验证界面</p>
                <div style="margin: 20px 0;">
                    <div style="width: 60px; height: 60px; border: 4px solid rgba(255,255,255,0.3); border-top: 4px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                </div>
                <p>正在验证中...</p>
                <button onclick="simulateSuccess()" style="margin: 10px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">模拟成功</button>
                <button onclick="simulateError()" style="margin: 10px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">模拟失败</button>
            `;

        const closeModal = () => {
          addLog("info", "关闭内嵌验证窗口");
          try {
            if (overlay && overlay.parentNode === document.body) {
              document.body.removeChild(overlay);
            }
          } catch (error) {
            addLog("warning", "移除 overlay 时出错: " + error.message);
          }
        };

        // 全局函数用于模拟验证结果
        window.simulateSuccess = () => {
          addLog("success", "内嵌验证成功", { token: "mock_token_12345" });
          setTimeout(closeModal, 1000);
        };

        window.simulateError = () => {
          addLog("error", "内嵌验证失败", { error: "mock_error" });
          setTimeout(closeModal, 1000);
        };

        closeButton.addEventListener("click", closeModal);
        overlay.addEventListener("click", (e) => {
          if (e.target === overlay) {
            closeModal();
          }
        });

        container.appendChild(closeButton);
        container.appendChild(content);
        overlay.appendChild(container);
        document.body.appendChild(overlay);

        addLog("success", "内嵌 iframe 验证窗口已创建");
      }

      // 基础模式测试
      function testBasicMode() {
        addLog("info", "开始测试基础模式");
        const container = document.getElementById("turnstile-container");
        container.innerHTML = `
                <div style="border: 2px dashed #ccc; padding: 20px; text-align: center; border-radius: 4px;">
                    <p>🔒 基础模式 Turnstile 组件</p>
                    <p>这里会渲染真实的 Cloudflare Turnstile 组件</p>
                    <button onclick="clearBasicTest()" style="margin-top: 10px; padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">清除</button>
                </div>
            `;
        addLog("success", "基础模式组件已渲染");
      }

      window.clearBasicTest = () => {
        document.getElementById("turnstile-container").innerHTML = "";
        addLog("info", "基础模式组件已清除");
      };

      // 清除日志
      function clearLogs() {
        document.getElementById("logContainer").innerHTML =
          '<div class="log-entry log-info">日志已清除</div>';
      }

      // 绑定事件
      document.getElementById("testEmbedded").addEventListener("click", testEmbeddedMode);
      document.getElementById("testBasic").addEventListener("click", testBasicMode);
      document.getElementById("clearLogs").addEventListener("click", clearLogs);

      // 添加 CSS 动画
      const style = document.createElement("style");
      style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
      document.head.appendChild(style);

      addLog("info", "CloudflareMgr 内嵌模式测试页面已加载");
    </script>
  </body>
</html>
