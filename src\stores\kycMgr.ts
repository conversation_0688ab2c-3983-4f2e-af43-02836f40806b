import { defineStore } from "pinia";
import { getUserStatus, getKycBonus, getUserKyc } from "@/api/user";
import { useGlobalStore } from "@/stores/global";
import { showToast } from "vant";
import { getGlobalDialog } from "@/enter/vant";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import router from "@/router";

export enum InGameType {
  Login = 0, //登录场景
  Withdraw, //提现 验证
  MyCenter, //个人中心
  GoThirdGame, //打开第三方游戏
  UNKNOWN = -1, // 未知
}

// KYC 认证状态
export enum KycState {
  NO_VERIFY = 0, // 未验证
  COMPLETE = 1, // 已认证
  REVIEWING = 2, // 审核中
  REJECTED = 3, // 已拒绝
  UNKNOWN = -1, // 未知状态
}

// KYC 认证类型
export enum KycSimple {
  CLOSE = 0, // 关闭状态
  NORMAL = 1, // 完整版认证
  SIMPLE = 2, // 简版认证
  UNKNOWN = -1, // 未知状态
}

// KYC 奖励信息接口
export interface KycBonusInfo {
  is_kyc_completed: number; // 是否完成 KYC (1: 已完成, 0: 未完成)
  kyc_completed_reward: string; // KYC 完成奖励金额
  status: number; // 显示状态 (1: 显示, 0: 不显示)
}

// 初始状态值
const initValue = {
  kycState: KycState.UNKNOWN,
  kycSimple: KycSimple.CLOSE,
  rejectMsg: "You are younger than 21.",
  kycVerifyCallback: undefined as ((state: boolean) => void) | undefined,
  //进入验证的 入口状态
  inGameType: InGameType.UNKNOWN,
  // 手机号绑定
  showVerifyPreconditionsDialog: false,
  kycBonus: {
    is_kyc_completed: 0, // 默认未完成
    kyc_completed_reward: "0", // 默认无奖励
    status: 0, // 默认不显示
  } as KycBonusInfo,
  kycFillInData: {},
};

export const useKycMgrStore = defineStore("kycMgr", {
  state: () => ({
    ...initValue,
  }),

  getters: {
    // 是否显示 KYC 提示
    showKycTip: (state) => {
      return state.kycBonus.status === 1 && Number(state.kycBonus.kyc_completed_reward) > 0;
    },

    // 是否已完成 KYC
    isKycCompleted: (state) => {
      return state.kycState === KycState.COMPLETE;
    },

    // 是否正在审核中
    isKycReviewing: (state) => {
      return state.kycState === KycState.REVIEWING;
    },

    // 是否被拒绝
    isKycRejected: (state) => {
      return state.kycState === KycState.REJECTED;
    },

    // 是否为简版 KYC
    isSimpleKyc: (state) => {
      return state.kycSimple === KycSimple.SIMPLE;
    },
  },

  actions: {
    // 获取 KYC 状态
    async fetchKycStatus(callback?: (response: any) => void) {
      const globalStore = useGlobalStore();
      if (!globalStore.token) return;
      try {
        // const res = {
        //   status: KycState.NO_VERIFY,
        //   is_full: KycSimple.NORMAL,
        //   reject: "You are younger than 21.",
        // };
        const res = await getUserStatus({});

        this.kycState = Number(res.status);
        this.kycSimple = Number(res.is_full);
        this.rejectMsg = res.reject || this.rejectMsg;

        callback?.(res);
        return res;
      } catch (error) {
        this.kycState = KycState.UNKNOWN;
        this.kycSimple = KycSimple.CLOSE;
      }
    },
    // 刷新 KYC 状态
    async refreshKycStatus() {
      const globalStore = useGlobalStore();
      if (!globalStore.token) return;
      try {
        const res = await getUserStatus({});
        this.kycState = Number(res.status);
        this.kycSimple = Number(res.is_full);
        this.rejectMsg = res.reject || this.rejectMsg;

        //已拒绝
        if (this.kycState === KycState.REJECTED) {
          this.rejectPop();
          return;
        }
        //审核中
        if (this.kycState === KycState.REVIEWING) {
          this.reviewPop();
          return;
        }
        //审核完成
        if (this.kycState === KycState.COMPLETE) {
          showToast("KYC Verification Certification Passed");
          return;
        }
      } catch (error) {}
    },

    // 获取 KYC 认证奖励信息
    async fetchKycBonus() {
      try {
        const res = await getKycBonus({});
        this.kycBonus = {
          is_kyc_completed: Number(res.is_kyc_completed) || 0,
          kyc_completed_reward: String(res.kyc_completed_reward) || "0",
          status: Number(res.status) || 0,
        };
      } catch (error) {
        console.error("获取 KYC 奖励信息失败:", error);
        // 保持默认值
      }
    },

    // 获取 KYC 填写数据
    async fetchUserKyc() {
      try {
        const res = await getUserKyc({});
        this.kycFillInData = res;
      } catch (error) {
        console.error("获取 KYC 奖励信息失败:", error);
        // 保持默认值
      }
    },

    // 重置 KYC 状态
    resetKycState() {
      this.kycState = KycState.UNKNOWN;
      this.kycSimple = KycSimple.CLOSE;
      this.kycBonus = { ...initValue.kycBonus };
    },

    // 更新 KYC 状态（用于其他地方手动更新）
    updateKycState(state: KycState, simple?: KycSimple) {
      this.kycState = state;
      if (simple !== undefined) {
        this.kycSimple = simple;
      }
    },

    backToTarget(state = false) {
      this.kycVerifyCallback?.(state);
      this.kycVerifyCallback = undefined;
    },
    /**
     * 入口方法：校验 KYC
     */
    async verifyKyc(inType: InGameType, callback?: (state: boolean) => void) {
      this.kycVerifyCallback = callback;
      this.inGameType = inType;

      const globalStore = useGlobalStore();
      if (globalStore.channel !== CHANEL_TYPE.WEB) {
        this.backToTarget(true);
        return;
      }
      if (this.kycState === KycState.UNKNOWN) {
        await this.fetchKycStatus(() => {
          this.goNext();
        });
      } else {
        this.goNext();
      }
    },

    async goNext() {
      // 关闭KYC 验证
      if (this.kycSimple === KycSimple.CLOSE) {
        this.backToTarget(true);
        return;
      }
      // 简版也放过
      if (this.kycSimple === KycSimple.SIMPLE) {
        this.backToTarget(true);
        return;
      }
      //未验证
      if (this.kycState === KycState.NO_VERIFY) {
        this.noVerifyPop();
        return;
      }
      //已拒绝
      if (this.kycState === KycState.REJECTED) {
        this.rejectPop();
        return;
      }
      //审核中
      if (this.kycState === KycState.REVIEWING) {
        switch (this.inGameType) {
          case InGameType.GoThirdGame:
            this.backToTarget(true);
            break;
          case InGameType.Withdraw:
            //提现 只在完整版 弹窗提示！
            if (this.kycSimple === KycSimple.NORMAL) {
              this.backToTarget(false);
            }
            this.reviewPop();
            break;
        }
        return;
      }
      this.backToTarget(true);
    },

    /**
     * 未提交
     */
    noVerifyPop() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "KYC Verification",
        message: `<div style="color:#222;text-align:center;">Your account is not yet fully verified</div>`,
        describe: `<div style="color:#999;text-align:center;">Your access to a certain service on the NUSTAR Online will be restricted.</div>`,
        confirmText: "Verify Now",
        onConfirm: async () => {
          //验证是否 绑定手机号
          this.checkPreconditions();
        },
        onCancel: () => {
          if (this.kycSimple === KycSimple.SIMPLE) {
            this.backToTarget(true);
          } else {
            this.backToTarget(false);
            // 退出登录
            this.logoutGame();
          }
        },
      });
    },

    /**
     * 审核拒绝
     */
    rejectPop() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "KYC Verification",
        message: `<div style="text-align:center">Review failed</div>`,
        describe: this.rejectMsg,
        onConfirm: async () => {
          //拒绝之后不用验证 输入手机号之类的 直接弹出kyc
          this.openKycVerify();
        },
        onCancel: () => {
          if (this.kycSimple === KycSimple.SIMPLE) {
            this.backToTarget(true);
          } else {
            this.backToTarget(false);
            // 退出登录
            this.logoutGame();
          }
        },
      });
    },

    /**
     *  审核中
     */
    reviewPop() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "KYC Verification",
        message: `<div style="margin-bottom:10px;text-align:center;color:#000">Estimated review time: 1 day.</div> `,
        describe: ` <div style="color:#666;text-align:center;">
        <div>Thank you for providing the required information. After the review is completed, we will notify you via an internal message.</div></div>
        <div  style="color:#666;text-align:center;margin-top:10px;">We are reviewing your application.</div>
        `,
        confirmText: "Refresh",
        onConfirm: async () => {
          //  刷新
          await this.refreshKycStatus();
        },
        onCancel: () => {},
      });
    },

    /**
     * 验证前置条件
     */
    checkPreconditions() {
      const globalStore = useGlobalStore();
      const user = globalStore.userInfo;
      if (!!user?.phone && !!user?.login_password) {
        // 如果 有手机号、登录密码 直接开始kyc
        this.openKycVerify();
      } else {
        this.showVerifyPreconditionsDialog = true;
      }
    },
    /**
     * 打开 KYC 验证弹窗
     */
    openKycVerify() {
      const globalStore = useGlobalStore();
      if (globalStore.channel == CHANEL_TYPE.MAYA) {
        return;
      }
      // 审核拒绝或者 审核完成后 不再弹出
      if (this.kycState === KycState.COMPLETE || this.kycState === KycState.REVIEWING) return;
      // 跳转kyc 验证弹窗
      if (this.kycSimple === KycSimple.SIMPLE) {
        router.push(`/kyc/simple-form`);
      } else if (this.kycSimple === KycSimple.NORMAL) {
        router.push(`/kyc/normal-form`);
      } else if (this.kycSimple === KycSimple.CLOSE) {
        router.push(`/kyc/simple-form`);
      }
    },

    logoutGame() {
      const globalStore = useGlobalStore();
      globalStore.loginOut();
    },
  },
});
