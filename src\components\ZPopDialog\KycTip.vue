<template>
  <ZDialog
    v-model="showKycTip"
    title="KYC Verification"
    :show-close="showCloseBtn"
    confirm-text="Verify Now"
    :on-confirm="handleConfirm"
    :showCancelButton="false"
    @handleClose="handleClose"
  >
    <div class="content">
      <div class="subtitle">{{ subTitle }}</div>
      <div class="desc">{{ desc }}</div>
    </div>
  </ZDialog>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useKycMgrStore, KycState, KycSimple } from "@/stores/kycMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";

const autoPopMgrStore = useAutoPopMgrStore();
const kycMgrStore = useKycMgrStore();
const { showKycTip, kycInfo } = storeToRefs(autoPopMgrStore);

// true 表示完整版、false表示简版
const forceValidateKyc = computed(() => {
  return kycInfo.value.is_full == KycSimple.NORMAL;
});

const subTitle = computed(() => {
  if (kycInfo.value.status == KycState.REJECTED) {
    return "Review failed";
  }
  return "Your account is not yet fully verified";
});

const showCloseBtn = computed(() => {
  if (forceValidateKyc.value) {
    return false;
  }
  return true;
});

const desc = computed(() => {
  if (kycInfo.value.status == KycState.REJECTED) {
    return kycInfo.value.reject;
  }
  return "Your access to a certain service on the NUSTAR Online will be restricted.";
});

const handleClose = () => {
  showKycTip.value = false;
  AutoPopMgr.destroyCurrentPopup();
};

const handleConfirm = async () => {
  showKycTip.value = false;
  kycMgrStore.checkPreconditions();
};

watch(
  () => autoPopMgrStore.showKycTip,
  (newVal) => {
    if (newVal) {
      window["kycDialogSourceLogin"] = false;
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  .title {
    font-size: 18px;
    font-weight: 800px;
    text-align: center;
  }

  .subtitle {
    margin-top: 10px;
    font-size: 14px;
    text-align: center;
  }

  .desc {
    margin-top: 10px;
    font-size: 14px;
    color: #666;
    text-align: center;
  }
}
</style>
