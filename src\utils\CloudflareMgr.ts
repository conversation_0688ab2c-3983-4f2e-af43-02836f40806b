/**
 * Cloudflare Turnstile 验证管理器
 * 提供统一的验证接口
 */

import { ALL_APP_SOURCE_CONFIG } from "./config/Config";
import { showToast } from "vant";
import { getToken } from "@/utils/auth";
import Request from "@/utils/http";
import { useGlobalStore } from "@/stores/global";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { myOS } from "@/utils/core/tools";

// CF Turnstile 验证场景类型
export enum CF_TURNSTILE_TYPE {
  /** 无 */
  NONE = "",
  /** 手机号注册登录-获取验证码 */
  LOGIN_PHONE_GET_CODE = "SCENE_GET_CODE",
  /** 登录-提交 */
  LOGIN_SUBMIT = "SCENE_LOGIN",
  /** 忘记密码-获取验证码 */
  FORGET_PW_GET_CODE = "SCENE_FORGET_PW_GET_CODE",
  /** 忘记密码-提交 */
  FORGET_PW_SUBMIT = "SCENE_FORGET_PASSWORD",
  /** 首次设定登录密码 */
  FIRST_SET_LOGIN_PW = "SCENE_FIRST_PASSWORD",
  /** 首次设定支付密码 */
  FIRST_SET_PAY_PW = "SCENE_FIRST_PAY_PASSWORD",
  /** 修改登录密码-获取验证码 */
  MODIFY_LOGIN_PW_GET_CODE = "SCENE_MODIFY_LOGIN_PW_GET_CODE",
  /** 修改登录密码-提交 */
  MODIFY_LOGIN_PW_SUBMIT = "SCENE_CHANGE_PASSWORD",
  /** 修改支付密码-获取验证码 */
  MODIFY_PAY_PW_GET_CODE = "xxx",
  /** 修改支付密码-提交 */
  MODIFY_PAY_PW_SUBMIT = "SCENE_CHANGE_PAY_PASSWORD",
  /** 绑定提款账号-获取验证码 */
  BIND_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 绑定提款账号-提交 */
  BIND_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_BIND_WITHDRAW_ACCOUNT",
  /** 修改提款账号-获取验证码 */
  MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 修改提款账号-提交 */
  MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_CHANGE_WITHDRAW_ACCOUNT",
  /** 提现-提交订单 */
  WITHDRAWAL_SUBMIT = "SCENE_WITHDRAW",
  /** 绑定手机号-获取验证码 */
  BIND_PHONE_GET_CODE = "xxx",
  /** 绑定手机号-提交 */
  BIND_PHONE_SUBMIT = "SCENE_BIND_PT_PHONE",
  /** 修改手机号-获取验证码 */
  MODIFY_PHONE_GET_CODE = "SCENE_MODIFY_PHONE_GET_CODE",
  /** 修改手机号-提交 */
  MODIFY_PHONE_SUBMIT = "SCENE_CHANGE_PT_PHONE",
  /** KYC 提交 */
  KYC_SUBMIT = "SCENE_SUB_KYC_INFO",
}

// CF Turnstile 配置接口
interface TurnstileConfig {
  siteKey: string;
  theme?: "light" | "dark" | "auto";
  size?: "normal" | "compact";
  language?: string;
  retry?: "auto" | "never";
  "retry-interval"?: number;
  "refresh-expired"?: "auto" | "manual" | "never";
  appearance?: "always" | "execute" | "interaction-only";
}

// CF Turnstile 响应接口
interface TurnstileResponse {
  success: boolean;
  token?: string;
  error?: string;
  metadata?: {
    action?: string;
    cdata?: string;
  };
}

export class CloudflareMgr {
  private cfType: string = "";
  private callback: Function | null = null;
  private turnstileToken: string = "";
  private widgetId: string = "";
  private isScriptLoaded: boolean = false;
  private loadingPromise: Promise<boolean> | null = null;
  private timeout: NodeJS.Timeout | null = null;

  // 外链模式相关属性
  private isExternalMode = false;
  private externalWindow: Window | null = null;
  private messageHandler: ((event: MessageEvent) => void) | null = null;

  // 单例模式
  static _instance: CloudflareMgr;
  static get instance() {
    if (this._instance) {
      return this._instance;
    }
    this._instance = new CloudflareMgr();
    return this._instance;
  }

  constructor() {
    this.initTurnstileScript();
  }

  /**
   * 初始化 Turnstile 脚本
   */
  private async initTurnstileScript(): Promise<boolean> {
    if (this.isScriptLoaded) {
      console.log("✅ Turnstile script already loaded");
      return true;
    }

    if (this.loadingPromise) {
      console.log("⏳ Turnstile script loading in progress...");
      return this.loadingPromise;
    }

    console.log("🚀 Initializing Cloudflare Turnstile script...");
    console.log("🌐 Current domain:", window.location.hostname);
    console.log("🔗 Current URL:", window.location.href);

    this.loadingPromise = new Promise((resolve) => {
      // 检查是否已经加载
      if (window.turnstile && typeof window.turnstile.render === "function") {
        this.isScriptLoaded = true;
        console.log("✅ Turnstile API already available");
        resolve(true);
        return;
      }

      // 检查是否已存在脚本标签
      const existingScript = document.querySelector('script[src*="challenges.cloudflare.com"]');
      if (existingScript) {
        console.log("📜 Turnstile script tag already exists, waiting for load...");

        // 等待现有脚本加载完成
        let attempts = 0;
        const maxAttempts = 50; // 10秒超时

        const checkExistingScript = () => {
          attempts++;
          if (window.turnstile && typeof window.turnstile.render === "function") {
            this.isScriptLoaded = true;
            console.log("✅ Existing Turnstile script loaded successfully");
            resolve(true);
          } else if (attempts >= maxAttempts) {
            console.error("❌ Existing Turnstile script failed to load within timeout");
            resolve(false);
          } else {
            setTimeout(checkExistingScript, 200);
          }
        };

        checkExistingScript();
        return;
      }

      // 动态加载 Turnstile 脚本
      console.log("📜 Creating new Turnstile script tag...");
      const script = document.createElement("script");
      script.src = "https://challenges.cloudflare.com/turnstile/v0/api.js";
      script.async = true;
      script.defer = true;
      script.crossOrigin = "anonymous";

      // 添加超时处理
      const timeout = setTimeout(() => {
        console.error("❌ Turnstile script loading timeout (15s)");
        script.remove();
        resolve(false);
      }, 15000);

      script.onload = () => {
        clearTimeout(timeout);
        console.log("✅ Cloudflare Turnstile script loaded successfully");

        // 验证 API 是否可用
        if (window.turnstile && typeof window.turnstile.render === "function") {
          this.isScriptLoaded = true;
          console.log("🔧 Available Turnstile methods:", Object.keys(window.turnstile));
          resolve(true);
        } else {
          console.error("❌ Turnstile script loaded but API not available");
          resolve(false);
        }
      };

      script.onerror = (error) => {
        clearTimeout(timeout);
        console.error("❌ Failed to load Cloudflare Turnstile script:", error);
        console.error("❌ Script URL:", script.src);
        console.error("❌ Network status:", navigator.onLine ? "Online" : "Offline");
        resolve(false);
      };

      document.head.appendChild(script);
      console.log("📜 Turnstile script tag added to document head");
    });

    return this.loadingPromise;
  }

  /**
   * 获取 Turnstile 配置
   */
  private getTurnstileConfig(): TurnstileConfig {
    const globalStore = useGlobalStore();

    return {
      siteKey: this.getSiteKey(),
      theme: "light",
      size: "normal",
      language: "en",
      retry: "auto",
      "retry-interval": 8000,
      "refresh-expired": "auto",
      appearance: "always",
    };
  }

  /**
   * 根据环境获取 Site Key
   */
  public getSiteKey(): string {
    const env = import.meta.env.MODE;
    let siteKey: string;

    // 根据不同环境返回不同的 Site Key
    switch (env) {
      case "development":
        siteKey = import.meta.env.VITE_CF_SITE_KEY_DEV || "0x4AAAAAABmR_Jwyy1tet7uX";
        break;
      case "test":
        siteKey = import.meta.env.VITE_CF_SITE_KEY_TEST || "0x4AAAAAABnByxp2v1NuTm7f";
        break;
      case "pre":
        siteKey = import.meta.env.VITE_CF_SITE_KEY_PRE || "0x4AAAAAABpKiQV8_G7FJy6p";
        break;
      case "production":
        siteKey = import.meta.env.VITE_CF_SITE_KEY_PROD || "0x4AAAAAABpKiQV8_G7FJy6p";
        break;
      default:
        siteKey = "0x4AAAAAABmR_Jwyy1tet7uX"; // 默认开发环境 Site Key
    }

    // 验证 Site Key 格式
    if (!this.validateSiteKey(siteKey)) {
      console.error("❌ Invalid Site Key format:", siteKey);
      console.warn("⚠️ Using fallback test Site Key");
      siteKey = "1x00000000000000000000AA"; // Cloudflare 官方测试 Site Key
    }

    // 确保返回的是字符串类型
    if (typeof siteKey !== "string") {
      console.warn("⚠️ Site key is not a string, using default test key");
      siteKey = "1x00000000000000000000AA";
    }

    console.log(`🔑 Using site key for ${env}:`, siteKey);
    console.log(`🌐 Current domain:`, window.location.hostname);
    return siteKey;
  }

  /**
   * 获取外链 Turnstile URL
   */
  private getTurnstileExternalUrl(siteKey: string, appearance = "always"): string {
    const host = import.meta.env.VITE_CF_TURNSTILE_HOST || "https://wayfocus.nustaronline.vip/";
    const os = this.getOSType();
    const isNative = this.isNativeEnvironment();

    const params = new URLSearchParams({
      siteKey: siteKey,
      appearance: appearance,
      isNative: isNative.toString(),
      os: os,
    });

    return `${host}turnstile.html?${params.toString()}`;
  }

  /**
   * 获取操作系统类型
   */
  private getOSType(): string {
    const os = myOS();
    const osString = String(os || "web");
    if (osString.includes("Android")) return "android";
    if (osString.includes("iOS") || osString.includes("iPhone") || osString.includes("iPad"))
      return "ios";
    return "web";
  }

  /**
   * 判断是否为原生环境
   */
  private isNativeEnvironment(): any {
    // 使用统一的配置源
    return ALL_APP_SOURCE_CONFIG.isNative;
  }

  /**
   * 验证 Site Key 格式
   */
  private validateSiteKey(siteKey: string): boolean {
    // Cloudflare Site Key 格式验证
    // 正常格式: 0x4AAAAAABxxxxxxxxxxxxxxx (长度通常为 25-30 字符)
    // 测试格式: 1x00000000000000000000AA (固定长度 25 字符)

    if (!siteKey || typeof siteKey !== "string") {
      return false;
    }

    // 检查是否是测试 Site Key
    if (siteKey === "1x00000000000000000000AA") {
      return true;
    }

    // 检查是否符合正常 Site Key 格式
    const siteKeyPattern = /^0x4[A-Za-z0-9_-]{20,25}$/;
    return siteKeyPattern.test(siteKey);
  }

  /**
   * 解析 Turnstile 错误代码
   */
  private parseErrorCode(error: string): { code: string; message: string; solution: string } {
    const errorCode = String(error).trim();

    // Cloudflare Turnstile 错误代码映射
    const errorMap: Record<string, { message: string; solution: string }> = {
      "110200": {
        message: "Site Key 无效或与当前域名不匹配",
        solution: "检查 Site Key 配置和域名设置。确保在 Cloudflare 控制台中正确配置了当前域名。",
      },
      "110201": {
        message: "Site Key 格式错误",
        solution: "检查 Site Key 格式是否正确。正确格式应为 0x4AAAAAABxxxxxxxxxxxxxxx",
      },
      "110202": {
        message: "Site Key 已被禁用",
        solution: "联系管理员检查 Cloudflare 控制台中的 Site Key 状态",
      },
      "110203": {
        message: "域名未授权",
        solution: "在 Cloudflare 控制台的 Turnstile 设置中添加当前域名",
      },
      "110204": {
        message: "请求频率过高",
        solution: "等待一段时间后重试，或检查是否有重复请求",
      },
      "110205": {
        message: "网络连接问题",
        solution: "检查网络连接，确保可以访问 Cloudflare 服务",
      },
      "110206": {
        message: "浏览器不支持",
        solution: "使用支持的浏览器版本，或启用 JavaScript",
      },
      "110207": {
        message: "验证超时",
        solution: "刷新页面重新尝试验证",
      },
      "110208": {
        message: "验证被阻止",
        solution: "检查广告拦截器或防火墙设置",
      },
      "110209": {
        message: "内部错误",
        solution: "稍后重试，如果问题持续存在请联系技术支持",
      },
      "110210": {
        message: "配置错误",
        solution: "检查 Turnstile 配置参数是否正确",
      },
    };

    const errorInfo = errorMap[errorCode] || {
      message: `未知错误代码: ${errorCode}`,
      solution: "请查看 Cloudflare Turnstile 文档或联系技术支持",
    };

    return {
      code: errorCode,
      message: errorInfo.message,
      solution: errorInfo.solution,
    };
  }

  /**
   * 设置超时处理
   */
  private setTurnstileTimeout(): void {
    this.timeout = setTimeout(() => {
      console.warn("⚠️ Cloudflare Turnstile verification timeout");
      closeZLoading();
      showToast("Verification timeout, please try again");
      this.callback?.(false);
    }, 30000); // 30秒超时
  }

  /**
   * 清除超时
   */
  private removeTimeout(): void {
    if (this.timeout) {
      clearTimeout(this.timeout);
      this.timeout = null;
    }
  }

  /**
   * 外链模式验证 (类似原生应用的 show 方法)
   * @param siteKey Site Key
   * @param appearance 外观设置
   * @param autoCloseWhenErr 错误时是否自动关闭
   */
  public show(siteKey: string, appearance = "always", autoCloseWhenErr = false): void {
    console.log("🚀 启动外链模式 Turnstile 验证");
    console.log("📋 参数:", { siteKey, appearance, autoCloseWhenErr });

    this.isExternalMode = true;

    // 构建外链 URL
    const url = this.getTurnstileExternalUrl(siteKey, appearance);
    console.log("🔗 外链 URL:", url);

    // 设置消息监听器
    this.setupMessageListener();

    // 打开外链窗口或 iframe
    if (this.isNativeEnvironment()) {
      // 原生环境：通知原生应用打开 WebView
      this.openInNativeWebView(url);
    } else {
      // Web 环境：打开新窗口或 iframe
      this.openInWebWindow(url);
    }
  }

  /**
   * 在原生 WebView 中打开
   */
  private openInNativeWebView(url: string): void {
    console.log("📱 在原生 WebView 中打开:", url);

    // 通知原生应用打开 WebView
    if ((window as any).webkit?.messageHandlers?.turnstile) {
      (window as any).webkit.messageHandlers.turnstile.postMessage({
        action: "openTurnstile",
        url: url,
      });
    } else if ((window as any).Android?.openTurnstile) {
      (window as any).Android.openTurnstile(url);
    } else {
      console.warn("⚠️ 原生接口不可用，回退到 Web 模式");
      this.openInWebWindow(url);
    }
  }

  /**
   * 在 Web 窗口中打开
   */
  private openInWebWindow(url: string): void {
    console.log("🌐 在 Web 窗口中打开:", url);

    // 打开新窗口
    this.externalWindow = window.open(
      url,
      "turnstile_verification",
      "width=400,height=600,scrollbars=yes,resizable=yes"
    );

    if (!this.externalWindow) {
      console.error("❌ 无法打开验证窗口，可能被弹窗拦截器阻止");
      showToast("无法打开验证窗口，请允许弹窗");
      this.callback?.(false);
      return;
    }

    // 监听窗口关闭
    const checkClosed = setInterval(() => {
      if (this.externalWindow?.closed) {
        clearInterval(checkClosed);
        console.log("🔒 验证窗口已关闭");
        this.cleanupExternalMode();
      }
    }, 1000);
  }

  /**
   * 设置消息监听器
   */
  private setupMessageListener(): void {
    if (this.messageHandler) {
      window.removeEventListener("message", this.messageHandler);
    }

    this.messageHandler = (event: MessageEvent) => {
      console.log("📨 收到消息:", event.data);

      // 验证消息来源
      const allowedOrigins = [
        import.meta.env.VITE_CF_TURNSTILE_HOST?.replace(/\/$/, ""),
        "https://wayfocus.nustaronline.vip",
      ];

      if (!allowedOrigins.some((origin) => event.origin.startsWith(origin))) {
        console.warn("⚠️ 忽略来自未知来源的消息:", event.origin);
        return;
      }

      this.handleExternalMessage(event.data);
    };

    window.addEventListener("message", this.messageHandler);
  }

  /**
   * 消息事件处理
   * @param data 消息数据
   */
  private handleExternalMessage(data: any): void {
    if (!data || typeof data !== "object") {
      return;
    }

    // 检查组件是否仍然有效
    if (!this.callback) {
      console.warn("⚠️ 回调函数不存在，忽略消息");
      return;
    }

    if (data.type === "onTurnstileSuccess") {
      console.log("✅ 外链验证成功");
      this.onVerifySuccess(data.token);
    } else if (data.type === "onTurnstileError") {
      console.error("❌ 外链验证失败:", data.error || "Unknown error");
      this.onVerifyFailed();
    } else if (data.type === "onTurnstileTimeout") {
      console.warn("⚠️ 外链验证超时");
      this.onVerifyFailed();
    } else if (data.type === "onTurnstileUnsupported") {
      console.warn("⚠️ 外链验证不支持");
      this.onVerifyFailed();
    } else if (data.type === "onTurnstileExpired") {
      console.warn("⚠️ 外链验证过期");
      this.onVerifyFailed();
    } else {
      console.log("📝 未知消息类型:", data.type);
    }
  }

  /**
   * 验证成功处理
   * @param token 验证令牌
   */
  private onVerifySuccess(token: string): void {
    this.onTurnstileSuccess(token);
    this.cleanupExternalMode();
  }

  /**
   * 验证失败处理
   */
  private onVerifyFailed(): void {
    this.onTurnstileError("External verification failed");
    this.cleanupExternalMode();
  }

  /**
   * 清理外链模式
   */
  private cleanupExternalMode(): void {
    if (this.externalWindow && !this.externalWindow.closed) {
      this.externalWindow.close();
    }
    this.externalWindow = null;

    if (this.messageHandler) {
      window.removeEventListener("message", this.messageHandler);
      this.messageHandler = null;
    }

    this.isExternalMode = false;
  }

  /**
   * 执行 Turnstile 验证
   * @param cfType 验证类型，使用 CF_TURNSTILE_TYPE 枚举
   * @param callback 回调函数
   * @param containerId 容器ID，如果不提供则创建临时容器
   * @param useExternalMode 是否使用外链模式
   */
  async turnstile_verify(
    cfType: CF_TURNSTILE_TYPE,
    callback: Function,
    containerId?: string,
    useExternalMode = false
  ): Promise<void> {
    try {
      this.cfType = cfType;
      this.callback = callback;

      // 如果启用外链模式，使用外链验证
      if (useExternalMode) {
        console.log("🔗 使用外链模式进行验证");
        const siteKey = this.getSiteKey();
        this.show(siteKey, "always", false);
        return;
      }

      // 清理之前的组件实例
      this.cleanupPreviousInstance();

      // 确保脚本已加载
      const scriptLoaded = await this.initTurnstileScript();
      if (!scriptLoaded) {
        console.error("❌ Turnstile script failed to load");
        showToast("Failed to load verification service");
        callback(false);
        return;
      }

      // 检查 Turnstile API 是否可用
      if (!window.turnstile || typeof window.turnstile.render !== "function") {
        console.error("❌ Turnstile API not available");
        showToast("Verification service not available");
        callback(false);
        return;
      }

      console.log("✅ Turnstile API ready");

      this.setTurnstileTimeout();
      showZLoading({ duration: 0, backgroundColor: "rgba(0, 0, 0, 0)" });

      // 创建或获取容器
      let container: HTMLElement;
      if (containerId) {
        const foundContainer = document.getElementById(containerId);
        if (!foundContainer) {
          throw new Error(`Container with id '${containerId}' not found`);
        }
        container = foundContainer;

        // 清空指定容器的内容
        container.innerHTML = "";
        console.log(`🧹 Cleaned specified container: ${containerId}`);
      } else {
        // 创建临时隐藏容器
        container = document.createElement("div");
        container.id = "cf-turnstile-temp-container";
        container.style.position = "fixed";
        container.style.top = "-9999px";
        container.style.left = "-9999px";
        document.body.appendChild(container);
      }

      const config = this.getTurnstileConfig();

      // 调试信息
      console.log("🔧 Turnstile config:", config);
      console.log("🔑 Site key type:", typeof config.siteKey, "value:", config.siteKey);
      console.log("🌐 Current domain:", window.location.hostname);
      console.log("🔗 Current URL:", window.location.href);

      // 验证 Site Key 有效性
      if (!this.validateSiteKey(config.siteKey)) {
        throw new Error(
          `Invalid Site Key format: ${config.siteKey}. Please check your environment configuration.`
        );
      }

      // 构建 Turnstile 选项
      const turnstileOptions = {
        sitekey: config.siteKey,
        theme: config.theme || "light",
        size: config.size || "normal",
        language: config.language || "en",
        retry: config.retry || "auto",
        "retry-interval": config["retry-interval"] || 8000,
        "refresh-expired": config["refresh-expired"] || "auto",
        appearance: config.appearance || "always",
        callback: (token: string) => {
          console.log("🎉 Turnstile verification successful, token received");
          console.log("🔑 Token length:", token?.length);
          this.onTurnstileSuccess(token);
        },
        "error-callback": (error: string) => {
          console.error("❌ Turnstile verification error:", error);
          console.error("❌ Error type:", typeof error);
          console.error("❌ Current domain:", window.location.hostname);
          console.error("❌ Site Key used:", config.siteKey);

          // 解析具体错误代码
          const errorInfo = this.parseErrorCode(error);
          console.error("❌ Error details:", errorInfo);

          this.onTurnstileError(error);
        },
        "expired-callback": () => {
          console.warn("⚠️ Turnstile verification expired");
          this.onTurnstileExpired();
        },
        "timeout-callback": () => {
          console.warn("⚠️ Turnstile verification timeout");
          this.onTurnstileTimeout();
        },
      };

      console.log("🚀 Turnstile options:", {
        ...turnstileOptions,
        callback: "[Function]",
        "error-callback": "[Function]",
        "expired-callback": "[Function]",
        "timeout-callback": "[Function]",
      });

      // 验证容器状态
      console.log("� Container info:", {
        id: container.id,
        className: container.className,
        innerHTML: container.innerHTML.length > 0 ? "Has content" : "Empty",
        offsetWidth: container.offsetWidth,
        offsetHeight: container.offsetHeight,
        isVisible: container.offsetParent !== null,
      });

      // 渲染 Turnstile 组件
      try {
        this.widgetId = window.turnstile.render(container, turnstileOptions);
        console.log("turnstileOptions:", turnstileOptions);
        console.log("✅ Turnstile widget rendered successfully, ID:", this.widgetId);
      } catch (renderError) {
        console.error("❌ Turnstile render failed:", renderError);
        throw renderError;
      }
    } catch (error) {
      console.error("❌ Cloudflare Turnstile verification failed:", error);
      this.removeTimeout();
      closeZLoading();
      callback(false);
    }
  }

  /**
   * Turnstile 验证成功回调
   */
  private onTurnstileSuccess(token: string): void {
    console.log("✅ Cloudflare Turnstile verification successful");
    this.removeTimeout();
    closeZLoading();
    this.turnstileToken = token;

    // 返回验证结果
    const result = {
      "cf-token": token,
      cf_type: this.cfType,
      success: true,
    };

    // 延迟清理，让用户看到成功状态
    setTimeout(() => {
      this.cleanupPreviousInstance();
    }, 3000);

    this.callback?.(result);
  }

  /**
   * Turnstile 验证失败回调
   */
  private onTurnstileError(error: string): void {
    console.error("❌ Cloudflare Turnstile verification error:", error);

    // 解析错误代码并显示详细信息
    const errorInfo = this.parseErrorCode(error);
    console.error("❌ Error details:", errorInfo);
    console.error("❌ Suggested solution:", errorInfo.solution);

    this.removeTimeout();
    closeZLoading();

    // 延迟清理，让用户看到状态
    setTimeout(() => {
      this.cleanupPreviousInstance();
    }, 3000);

    // 根据错误代码显示不同的提示信息
    let toastMessage = "Verification failed, please try again";

    switch (error) {
      case "110200":
        toastMessage = "Site Key configuration error. Please contact support.";
        break;
      case "110203":
        toastMessage = "Domain not authorized. Please contact support.";
        break;
      case "110204":
        toastMessage = "Too many requests. Please wait and try again.";
        break;
      case "110205":
        toastMessage = "Network error. Please check your connection.";
        break;
      case "110207":
        toastMessage = "Verification timeout. Please try again.";
        break;
      case "110208":
        toastMessage = "Verification blocked. Please disable ad blockers.";
        break;
      default:
        toastMessage = `Verification failed (${error}). Please try again.`;
    }

    showToast(toastMessage);
    this.callback?.(false);
  }

  /**
   * Turnstile 验证过期回调
   */
  private onTurnstileExpired(): void {
    console.warn("⚠️ Cloudflare Turnstile verification expired");
    this.removeTimeout();
    closeZLoading();
    // 延迟清理，让用户看到状态
    setTimeout(() => {
      this.cleanupPreviousInstance();
    }, 3000);
    showToast("Verification expired, please try again");
    this.callback?.(false);
  }

  /**
   * Turnstile 验证超时回调
   */
  private onTurnstileTimeout(): void {
    console.warn("⚠️ Cloudflare Turnstile verification timeout");
    this.removeTimeout();
    closeZLoading();
    // 延迟清理，让用户看到状态
    setTimeout(() => {
      this.cleanupPreviousInstance();
    }, 3000);
    showToast("Verification timeout, please try again");
    this.callback?.(false);
  }

  /**
   * 清理之前的组件实例
   */
  private cleanupPreviousInstance(): void {
    // 移除之前的 widget
    if (this.widgetId && window.turnstile && typeof window.turnstile.remove === "function") {
      try {
        window.turnstile.remove(this.widgetId);
        console.log("🧹 Removed previous Turnstile widget:", this.widgetId);
      } catch (error) {
        console.warn("⚠️ Failed to remove previous widget:", error);
      }
      this.widgetId = "";
    }

    // 清理所有可能的容器中的 Turnstile 内容
    this.cleanupAllTurnstileContainers();

    // 清理临时容器
    this.cleanupTempContainer();

    // 重置状态
    this.turnstileToken = "";
    this.removeTimeout();
  }

  /**
   * 清理所有 Turnstile 容器中的内容
   */
  private cleanupAllTurnstileContainers(): void {
    // 常见的 Turnstile 容器 ID 列表
    const containerIds = [
      "verify-dialog-turnstile-container",
      "debug-turnstile-container",
      "turnstile-container",
      "cf-turnstile-container",
      "cf-turnstile-temp-container",
    ];

    containerIds.forEach((id) => {
      const container = document.getElementById(id);
      if (container) {
        // 清空容器内容，但保留容器本身
        container.innerHTML = "";
        console.log(`🧹 Cleaned container: ${id}`);
      }
    });

    // 清理所有包含 cf-turnstile 类的元素
    const turnstileElements = document.querySelectorAll(".cf-turnstile, [data-sitekey]");
    turnstileElements.forEach((element) => {
      if (element.parentNode) {
        element.remove();
        console.log("🧹 Removed orphaned Turnstile element");
      }
    });
  }

  /**
   * 清理临时容器
   */
  private cleanupTempContainer(): void {
    const tempContainer = document.getElementById("cf-turnstile-temp-container");
    if (tempContainer) {
      tempContainer.remove();
    }
  }

  /**
   * 重置 Turnstile 组件
   */
  reset(): void {
    if (this.widgetId && window.turnstile) {
      window.turnstile.reset(this.widgetId);
    }
  }

  /**
   * 移除 Turnstile 组件
   */
  remove(): void {
    this.cleanupPreviousInstance();
  }

  /**
   * 获取当前 token
   */
  getToken(): string {
    return this.turnstileToken;
  }

  /**
   * 清除当前 token
   */
  clearToken(): void {
    this.turnstileToken = "";
  }
}

// 全局类型声明
declare global {
  interface Window {
    turnstile: {
      render: (container: HTMLElement | string, options: any) => string;
      reset: (widgetId: string) => void;
      remove: (widgetId: string) => void;
      getResponse: (widgetId: string) => string;
    };
  }
}
