<!-- 通用表单输入组件 -->
<template>
  <div class="z-form-field" :class="{ 'has-error': hasError, 'is-disabled': disabled }">
    <!-- 标签 -->
    <label v-if="label" class="field-label" :for="fieldId">
      {{ label }}
      <!-- <span v-if="required" class="required-mark">*</span> -->
      <span v-if="!required" class="un-required">(Optional)</span>
    </label>

    <!-- 输入框容器 -->
    <div class="field-container">
      <van-field
        :id="fieldId"
        v-model="inputValue"
        :type="fieldType"
        :inputmode="inputMode"
        :placeholder="placeholder"
        :maxlength="maxLength"
        :minlength="minLength"
        :disabled="disabled"
        :readonly="readonly"
        :clearable="clearable"
        :show-word-limit="showWordLimit"
        :error="hasError"
        :error-message="errorMessage"
        :rules="computedRules"
        class="field-input"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        @clear="handleClear"
        @paste="handlePaste"
        @keydown="handleKeydown"
        @compositionstart="handleCompositionStart"
        @compositionend="handleCompositionEnd"
      >
        <!-- 左侧插槽 -->
        <template #left-icon v-if="$slots.leftIcon">
          <slot name="leftIcon"></slot>
        </template>

        <!-- 右侧插槽 -->
        <template #right-icon v-if="$slots.rightIcon">
          <slot name="rightIcon"></slot>
        </template>

        <!-- 按钮插槽 -->
        <template #button v-if="$slots.button">
          <slot name="button"></slot>
        </template>
      </van-field>
    </div>

    <!-- 错误提示 -->
    <div v-if="hasError && showErrorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 帮助文本 -->
    <div v-if="helpText && !hasError" class="help-text">
      {{ helpText }}
    </div>

    <!-- 默认插槽（底部额外内容） -->
    <div v-if="$slots.default" class="default-slot">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import { generateId } from "@/utils/core/tools";

// 输入类型枚举
export type InputType =
  | "text"
  | "number"
  | "digit"
  | "tel"
  | "email"
  | "password"
  | "search"
  | "url";

// 输入模式枚举
export type InputMode = "text" | "numeric" | "decimal" | "tel" | "email" | "url" | "search";

// 输入限制类型
export type InputRestriction =
  | "none"
  | "alphanumeric"
  | "alphabetic"
  | "numeric"
  | "phone"
  | "email";

// 验证规则类型
export interface ValidationRule {
  required?: boolean;
  pattern?: RegExp;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  validator?: (value: string) => boolean | string;
  message?: string;
}

interface Props {
  /** 双向绑定的值 */
  modelValue?: string | number;
  /** 输入框类型 */
  type?: InputType;
  /** 输入模式 */
  inputmode?: InputMode;
  /** 标签文本 */
  label?: string;
  /** 标签文本别名 */
  labelAlias?: string;
  /** 占位符 */
  placeholder?: string;
  /** 最大长度 */
  maxLength?: number;
  /** 最小长度 */
  minLength?: number;
  /** 是否必填 */
  required?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否只读 */
  readonly?: boolean;
  /** 是否可清除 */
  clearable?: boolean;
  /** 是否显示字数统计 */
  showWordLimit?: boolean;
  /** 是否显示错误信息 */
  showErrorMessage?: boolean;
  /** 帮助文本 */
  helpText?: string;
  /** 输入限制类型 */
  restriction?: InputRestriction;
  /** 自定义验证规则 */
  rules?: ValidationRule[];
  /** 是否在输入时验证 */
  validateOnInput?: boolean;
  /** 是否在失焦时验证 */
  validateOnBlur?: boolean;
  /** 自定义输入过滤器 */
  inputFilter?: (value: string) => string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  type: "text",
  inputmode: "text",
  label: "",
  labelAlias: "",
  placeholder: "",
  maxLength: undefined,
  minLength: undefined,
  required: true,
  disabled: false,
  readonly: false,
  clearable: true,
  showWordLimit: false,
  showErrorMessage: true,
  helpText: "",
  restriction: "none",
  rules: () => [],
  validateOnInput: false,
  validateOnBlur: true,
  inputFilter: undefined,
});

interface Emits {
  (e: "update:modelValue", value: string | number): void;
  (e: "input", value: string): void;
  (e: "blur", event: Event): void;
  (e: "focus", event: Event): void;
  (e: "clear"): void;
  (e: "validate", isValid: boolean, message?: string): void;
}

const emit = defineEmits<Emits>();

// 内部状态
const inputValue = ref(String(props.modelValue || ""));
const hasError = ref(false);
const errorMessage = ref("");
const fieldId = generateId("field");
const isComposing = ref(false); // 输入法组合状态

// 计算属性
const fieldType = computed(() => {
  if (props.restriction === "numeric" || props.restriction === "phone") {
    return "digit";
  }
  return props.type;
});

const inputMode = computed(() => {
  if (props.restriction === "numeric" || props.restriction === "phone") {
    return "numeric";
  }
  if (props.restriction === "email") {
    return "email";
  }
  return props.inputmode;
});

// 计算验证规则
const computedRules = computed(() => {
  const rules: any[] = [];

  // 必填验证
  if (props.required) {
    rules.push({
      required: true,
      message: `${props.labelAlias || props.label || "This field"} is required`,
    });
  }

  // 长度验证
  if (props.minLength) {
    rules.push({
      minLength: props.minLength,
      message: `Minimum ${props.minLength} characters required`,
    });
  }

  if (props.maxLength) {
    rules.push({
      maxLength: props.maxLength,
      message: `Maximum ${props.maxLength} characters allowed`,
    });
  }

  // 添加自定义规则
  rules.push(...props.rules);

  return rules;
});

// 输入限制过滤器
const applyInputRestriction = (value: string): string => {
  switch (props.restriction) {
    case "alphanumeric":
      return value.replace(/[^a-zA-Z0-9]/g, "");
    case "alphabetic":
      return value.replace(/[^a-zA-Z\s]/g, "");
    case "numeric":
      return value.replace(/[^0-9]/g, "");
    case "phone":
      return value.replace(/[^0-9]/g, "").slice(0, 11);
    case "email":
      return value.replace(/[^a-zA-Z0-9@._-]/g, "");
    default:
      return value;
  }
};

// 检查字符是否被允许
const isCharacterAllowed = (char: string): boolean => {
  switch (props.restriction) {
    case "alphanumeric":
      return /[a-zA-Z0-9]/.test(char);
    case "alphabetic":
      return /[a-zA-Z\s]/.test(char);
    case "numeric":
      return /[0-9]/.test(char);
    case "phone":
      return /[0-9]/.test(char);
    case "email":
      return /[a-zA-Z0-9@._-]/.test(char);
    default:
      return true;
  }
};

// 验证输入值
const validateValue = (value: string): { isValid: boolean; message?: string } => {
  // 重置错误状态
  hasError.value = false;
  errorMessage.value = "";

  // 必填验证
  if (props.required && !value.trim()) {
    return {
      isValid: false,
      message: `${props.labelAlias || props.label || "This field"} is required`,
    };
  }

  // 长度验证
  if (props.minLength && value.length < props.minLength) {
    return {
      isValid: false,
      message: `Minimum ${props.minLength} characters required`,
    };
  }

  if (props.maxLength && value.length > props.maxLength) {
    return {
      isValid: false,
      message: `Maximum ${props.maxLength} characters allowed`,
    };
  }

  // 自定义规则验证
  for (const rule of props.rules) {
    if (rule.pattern && !rule.pattern.test(value)) {
      return {
        isValid: false,
        message: rule.message || "Invalid format",
      };
    }

    if (rule.validator) {
      const result = rule.validator(value);
      if (result !== true) {
        return {
          isValid: false,
          message: typeof result === "string" ? result : rule.message || "Validation failed",
        };
      }
    }
  }

  return { isValid: true };
};

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value;

  // 如果是输入法组合状态，暂时不应用限制
  if (isComposing.value) {
    inputValue.value = value;
    return;
  }

  // 应用输入限制
  let filteredValue = applyInputRestriction(value);

  // 应用自定义过滤器
  if (props.inputFilter) {
    filteredValue = props.inputFilter(filteredValue);
  }

  // 长度限制
  if (props.maxLength && filteredValue.length > props.maxLength) {
    filteredValue = filteredValue.slice(0, props.maxLength);
  }

  // 如果过滤后的值与原值不同，需要更新输入框
  if (filteredValue !== value) {
    inputValue.value = filteredValue;
    // 使用 nextTick 确保 DOM 更新
    nextTick(() => {
      if (target.value !== filteredValue) {
        target.value = filteredValue;
      }
    });
  } else {
    inputValue.value = filteredValue;
  }

  // 更新父组件
  const outputValue = props.type === "number" ? Number(filteredValue) || 0 : filteredValue;
  emit("update:modelValue", outputValue);
  emit("input", filteredValue);

  // 输入时验证
  if (props.validateOnInput) {
    const validation = validateValue(filteredValue);
    hasError.value = !validation.isValid;
    errorMessage.value = validation.message || "";
    emit("validate", validation.isValid, validation.message);
  }
};

const handleBlur = (event: Event) => {
  emit("blur", event);
  // 失焦时验证
  if (props.validateOnBlur) {
    const validation = validateValue(inputValue.value);
    hasError.value = !validation.isValid;
    errorMessage.value = validation.message || "";
    emit("validate", validation.isValid, validation.message);
  }
};

const handleFocus = (event: Event) => {
  emit("focus", event);

  // 聚焦时清除错误状态
  if (hasError.value) {
    hasError.value = false;
    errorMessage.value = "";
  }
};

const handleClear = () => {
  inputValue.value = "";
  emit("update:modelValue", props.type === "number" ? 0 : "");
  emit("clear");

  // 清除错误状态
  hasError.value = false;
  errorMessage.value = "";
};

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault();

  const pastedText = event.clipboardData?.getData("text") || "";
  const filteredText = applyInputRestriction(pastedText);

  // 应用长度限制
  let finalText = filteredText;
  if (props.maxLength && finalText.length > props.maxLength) {
    finalText = finalText.slice(0, props.maxLength);
  }

  // 更新值
  inputValue.value = finalText;
  const outputValue = props.type === "number" ? Number(finalText) || 0 : finalText;
  emit("update:modelValue", outputValue);
  emit("input", finalText);
};

// 处理键盘按下事件
const handleKeydown = (event: KeyboardEvent) => {
  // 如果是输入法组合状态，不处理
  if (isComposing.value) return;

  // 允许的控制键
  const allowedKeys = [
    "Backspace",
    "Delete",
    "Tab",
    "Escape",
    "Enter",
    "ArrowLeft",
    "ArrowRight",
    "ArrowUp",
    "ArrowDown",
    "Home",
    "End",
    "PageUp",
    "PageDown",
  ];

  // 允许 Ctrl/Cmd 组合键（复制、粘贴、全选等）
  if (event.ctrlKey || event.metaKey) {
    return;
  }

  // 如果是控制键，允许通过
  if (allowedKeys.includes(event.key)) {
    return;
  }

  // 检查输入的字符是否被允许
  if (event.key.length === 1 && !isCharacterAllowed(event.key)) {
    event.preventDefault();
  }

  // 检查长度限制
  if (props.maxLength && inputValue.value.length >= props.maxLength && event.key.length === 1) {
    event.preventDefault();
  }
};

// 处理输入法开始
const handleCompositionStart = () => {
  isComposing.value = true;
};

// 处理输入法结束
const handleCompositionEnd = (event: CompositionEvent) => {
  isComposing.value = false;

  // 输入法结束后，应用输入限制
  const target = event.target as HTMLInputElement;
  const value = target.value;
  const filteredValue = applyInputRestriction(value);

  if (filteredValue !== value) {
    inputValue.value = filteredValue;
    const outputValue = props.type === "number" ? Number(filteredValue) || 0 : filteredValue;
    emit("update:modelValue", outputValue);
    emit("input", filteredValue);
  }
};

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = String(newValue || "");
  },
  { immediate: true }
);

// 暴露验证方法
const validate = () => {
  const validation = validateValue(inputValue.value);
  hasError.value = !validation.isValid;
  errorMessage.value = validation.message || "";
  emit("validate", validation.isValid, validation.message);
  return validation.isValid;
};

const clearValidation = () => {
  hasError.value = false;
  errorMessage.value = "";
};

defineExpose({
  validate,
  clearValidation,
  focus: () => nextTick(() => document.getElementById(fieldId)?.focus()),
  blur: () => nextTick(() => document.getElementById(fieldId)?.blur()),
});
</script>

<style lang="scss" scoped>
.z-form-field {
  margin-bottom: 16px;

  &.has-error {
    .field-input {
      :deep(.van-field__control) {
        color: #ff4757;
      }
    }
  }

  &.is-disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.field-label {
  display: block;
  margin-bottom: 8px;
  color: #666;
  font-family: "Inter";
  font-size: 14px;
  font-weight: 400;
  line-height: normal;

  .required-mark {
    color: #ff4757;
    margin-left: 2px;
  }
  .un-required {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
  }
}

.field-container {
  position: relative;
}

.field-input {
  padding-left: 0;
  padding-right: 0;
  :deep(.van-field__control) {
    --van-field-border: none;
    --van-field-padding: 8px 0;
    background-color: #f7f8fa;
    border-radius: 999px;
    height: 42px;
    box-sizing: border-box;
    padding: 12px 20px;
    color: #333;
    font-family: D-DIN;
    font-size: 14px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -0.3px;
    border: 1px solid transparent;

    &::placeholder {
      color: #c0c0c0;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    &:focus {
      outline: none;
      border-color: rgba(172, 17, 64, 0.2);
    }
  }

  :deep(.van-field__error-message) {
    display: none; // 使用自定义错误显示
  }
}

.error-message {
  color: #ff4757;
  font-size: 12px;
  font-family: Inter;
  line-height: 1.4;
}

.help-text {
  margin-top: 4px;
  color: #999;
  font-size: 12px;
  font-family: Inter;
  line-height: 1.4;
}
</style>
