<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Key 调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Site Key 调试工具</h1>
        <p>这个页面用于调试 Site Key 的获取和类型问题。</p>
        
        <div id="debug-output"></div>
        
        <button onclick="debugSiteKey()">调试 Site Key</button>
        <button onclick="clearOutput()">清除输出</button>
    </div>

    <script>
        function addDebugInfo(type, message, data = null) {
            const output = document.getElementById('debug-output');
            const div = document.createElement('div');
            div.className = `debug-info ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            if (data !== null) {
                div.innerHTML += `<br>数据: ${JSON.stringify(data, null, 2)}`;
            }
            output.appendChild(div);
        }

        function clearOutput() {
            document.getElementById('debug-output').innerHTML = '';
        }

        function debugSiteKey() {
            addDebugInfo('info', '开始调试 Site Key...');
            
            // 模拟环境变量
            const mockEnv = {
                MODE: 'development',
                VITE_CF_SITE_KEY_DEV: '0x4AAAAAABmR_Jwyy1tet7uX',
                VITE_CF_SITE_KEY_TEST: '0x4AAAAAABnByxp2v1NuTm7f',
                VITE_CF_SITE_KEY_PRE: '0x4AAAAAABpKiQV8_G7FJy6p',
                VITE_CF_SITE_KEY_PROD: '0x4AAAAAABpKiQV8_G7FJy6p'
            };

            addDebugInfo('info', '模拟环境变量', mockEnv);

            // 模拟 getSiteKey 逻辑
            const env = mockEnv.MODE;
            let siteKey;

            switch (env) {
                case "development":
                    siteKey = mockEnv.VITE_CF_SITE_KEY_DEV || "0x4AAAAAABmR_Jwyy1tet7uX";
                    break;
                case "test":
                    siteKey = mockEnv.VITE_CF_SITE_KEY_TEST || "0x4AAAAAABnByxp2v1NuTm7f";
                    break;
                case "pre":
                    siteKey = mockEnv.VITE_CF_SITE_KEY_PRE || "0x4AAAAAABpKiQV8_G7FJy6p";
                    break;
                case "production":
                    siteKey = mockEnv.VITE_CF_SITE_KEY_PROD || "0x4AAAAAABpKiQV8_G7FJy6p";
                    break;
                default:
                    siteKey = "0x4AAAAAABmR_Jwyy1tet7uX";
            }

            addDebugInfo('info', `原始 siteKey (${env})`, {
                value: siteKey,
                type: typeof siteKey,
                length: siteKey?.length,
                isString: typeof siteKey === 'string'
            });

            // 强制转换为字符串
            siteKey = String(siteKey || "");
            
            addDebugInfo('info', '转换后的 siteKey', {
                value: siteKey,
                type: typeof siteKey,
                length: siteKey.length,
                isString: typeof siteKey === 'string',
                isEmpty: !siteKey
            });

            // 验证格式
            const isValid = validateSiteKey(siteKey);
            addDebugInfo(isValid ? 'success' : 'error', `Site Key 格式验证: ${isValid ? '通过' : '失败'}`);

            // 模拟 Turnstile 选项构建
            const turnstileOptions = {
                sitekey: String(siteKey),
                theme: "light",
                size: "normal"
            };

            addDebugInfo('info', 'Turnstile 选项', {
                sitekey: turnstileOptions.sitekey,
                sitekeyType: typeof turnstileOptions.sitekey,
                sitekeyLength: turnstileOptions.sitekey.length
            });

            // 最终验证
            if (typeof turnstileOptions.sitekey === 'string') {
                addDebugInfo('success', '✅ Site Key 类型验证通过');
            } else {
                addDebugInfo('error', '❌ Site Key 类型验证失败');
            }
        }

        function validateSiteKey(siteKey) {
            if (!siteKey || typeof siteKey !== "string") {
                return false;
            }

            // 检查是否是测试 Site Key
            if (siteKey === "1x00000000000000000000AA") {
                return true;
            }

            // 检查是否符合正常 Site Key 格式
            const siteKeyPattern = /^0x4[A-Za-z0-9_-]{20,25}$/;
            return siteKeyPattern.test(siteKey);
        }

        // 页面加载时自动运行一次
        window.addEventListener('load', () => {
            addDebugInfo('info', '页面已加载，可以开始调试');
        });
    </script>
</body>
</html>
