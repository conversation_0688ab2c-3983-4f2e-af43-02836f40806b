<template>
  <div class="cloudflare-test">
    <div class="test-header">
      <h2>🔐 Cloudflare Turnstile 验证测试</h2>
      <p>集成验证、诊断、外链模式等所有功能的综合测试页面</p>
    </div>

    <!-- 功能选项卡 -->
    <van-tabs v-model:active="activeTab" @change="onTabChange">
      <!-- 基础验证测试 -->
      <van-tab title="基础验证" name="basic">
        <div class="tab-content">
          <div class="section">
            <h3>🧪 验证测试</h3>
            <div class="test-controls">
              <van-button
                type="primary"
                @click="testBasicVerification"
                :loading="isTestingBasic"
                block
              >
                测试基础验证
              </van-button>

              <van-button
                type="success"
                @click="testExternalVerification"
                :loading="isTestingExternal"
                block
                style="margin-top: 12px"
              >
                测试外链验证
              </van-button>
            </div>

            <!-- Turnstile 容器 -->
            <div id="turnstile-container" v-show="showTurnstile" class="turnstile-container"></div>
          </div>
        </div>
      </van-tab>

      <!-- 环境诊断 -->
      <van-tab title="环境诊断" name="diagnostic">
        <div class="tab-content">
          <div class="section">
            <h3>🌐 环境信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">当前环境:</span>
                <span class="value">{{ currentEnv }}</span>
              </div>
              <div class="info-item">
                <span class="label">操作系统:</span>
                <span class="value">{{ osType }}</span>
              </div>
              <div class="info-item">
                <span class="label">是否原生:</span>
                <span class="value">{{ isNative ? "是" : "否" }}</span>
              </div>
              <div class="info-item">
                <span class="label">Site Key:</span>
                <span class="value monospace">{{ currentSiteKey }}</span>
              </div>
            </div>
          </div>

          <div class="section">
            <h3>🔧 诊断操作</h3>
            <div class="diagnostic-buttons">
              <van-button @click="runDiagnostic" :loading="isDiagnosing"> 运行诊断 </van-button>
              <van-button @click="checkSiteKey"> 检查 Site Key </van-button>
              <van-button @click="testScriptLoading"> 测试脚本 </van-button>
              <van-button @click="clearCache" type="warning"> 清除缓存 </van-button>
            </div>
          </div>
        </div>
      </van-tab>

      <!-- 外链配置 -->
      <van-tab title="外链配置" name="external">
        <div class="tab-content">
          <div class="section">
            <h3>⚙️ 外链配置</h3>
            <div class="config-form">
              <van-field
                v-model="externalConfig.siteKey"
                label="Site Key"
                placeholder="选择 Site Key"
                readonly
                @click="showSiteKeyPicker = true"
              />
              <van-field
                v-model="externalConfig.appearance"
                label="外观设置"
                placeholder="选择外观"
                readonly
                @click="showAppearancePicker = true"
              />
              <van-field
                v-model="externalConfig.host"
                label="外链域名"
                placeholder="输入外链域名"
              />
            </div>

            <div class="external-actions">
              <van-button type="primary" @click="previewExternalUrl" block>
                预览外链 URL
              </van-button>
              <van-button type="default" @click="copyExternalUrl" block style="margin-top: 12px">
                复制外链 URL
              </van-button>
            </div>
          </div>
        </div>
      </van-tab>

      <!-- 测试结果 -->
      <van-tab title="测试结果" name="results">
        <div class="tab-content">
          <div class="section">
            <h3>📊 测试日志</h3>
            <div class="results-container">
              <div v-for="(log, index) in testLogs" :key="index" class="log-item" :class="log.type">
                <div class="log-content">
                  <span class="log-time">{{ log.time }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
                <div v-if="log.data" class="log-data">
                  <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
                </div>
              </div>
            </div>

            <div class="results-actions">
              <van-button @click="clearLogs" type="warning" size="small"> 清除日志 </van-button>
              <van-button @click="exportLogs" type="default" size="small"> 导出日志 </van-button>
            </div>
          </div>
        </div>
      </van-tab>
    </van-tabs>

    <!-- Site Key 选择器 -->
    <van-popup v-model:show="showSiteKeyPicker" position="bottom">
      <van-picker
        :columns="siteKeyOptions"
        @confirm="onSiteKeyConfirm"
        @cancel="showSiteKeyPicker = false"
      />
    </van-popup>

    <!-- 外观选择器 -->
    <van-popup v-model:show="showAppearancePicker" position="bottom">
      <van-picker
        :columns="appearanceOptions"
        @confirm="onAppearanceConfirm"
        @cancel="showAppearancePicker = false"
      />
    </van-popup>

    <!-- URL 预览弹窗 -->
    <van-dialog
      v-model:show="showUrlDialog"
      title="外链 URL 预览"
      :message="previewUrl"
      show-cancel-button
      confirm-button-text="复制链接"
      @confirm="copyUrlToClipboard"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { showToast } from "vant";
import { CloudflareMgr, CF_TURNSTILE_TYPE } from "@/utils/CloudflareMgr";
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";

// 响应式状态
const activeTab = ref("basic");
const currentEnv = ref(import.meta.env.MODE);
const osType = ref("");
const isNative = ref(false);
const currentSiteKey = ref("");

// 测试状态
const isTestingBasic = ref(false);
const isTestingExternal = ref(false);
const isDiagnosing = ref(false);
const showTurnstile = ref(false);

// 外链配置
const externalConfig = ref({
  siteKey: "",
  appearance: "always",
  host: import.meta.env.VITE_CF_TURNSTILE_HOST || "https://wayfocus.nustaronline.vip/",
});

// 选择器状态
const showSiteKeyPicker = ref(false);
const showAppearancePicker = ref(false);
const showUrlDialog = ref(false);
const previewUrl = ref("");

// 测试日志
const testLogs = ref<
  Array<{
    type: "success" | "error" | "warning" | "info";
    message: string;
    time: string;
    data?: any;
  }>
>([]);

// 选择器选项
const siteKeyOptions = [
  { text: "开发环境", value: import.meta.env.VITE_CF_SITE_KEY_DEV },
  { text: "测试环境", value: import.meta.env.VITE_CF_SITE_KEY_TEST },
  { text: "预发布环境", value: import.meta.env.VITE_CF_SITE_KEY_PRE },
  { text: "生产环境", value: import.meta.env.VITE_CF_SITE_KEY_PROD },
  { text: "官方测试", value: "1x00000000000000000000AA" },
];

const appearanceOptions = [
  { text: "总是显示", value: "always" },
  { text: "交互时显示", value: "interaction-only" },
  { text: "执行时显示", value: "execute" },
];

// 计算属性
const mgr = computed(() => CloudflareMgr.instance);

// 添加日志
const addLog = (type: "success" | "error" | "warning" | "info", message: string, data?: any) => {
  const time = new Date().toLocaleTimeString();
  testLogs.value.unshift({ type, message, time, data });

  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50);
  }
};

// 标签页切换
const onTabChange = (name: string) => {
  addLog("info", `切换到 ${name} 标签页`);
};

// 基础验证测试
const testBasicVerification = async () => {
  if (isTestingBasic.value) return;

  isTestingBasic.value = true;
  showTurnstile.value = true;
  addLog("info", "开始基础验证测试");

  try {
    await mgr.value.turnstile_verify(
      CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
      (result: any) => {
        isTestingBasic.value = false;

        if (result === false) {
          addLog("error", "基础验证失败");
          showToast("验证失败");
          return;
        }

        addLog("success", "基础验证成功", result);
        showToast("验证成功");

        // 隐藏组件
        setTimeout(() => {
          showTurnstile.value = false;
        }, 2000);
      },
      "turnstile-container",
      false // 不使用外链模式
    );
  } catch (error) {
    isTestingBasic.value = false;
    showTurnstile.value = false;
    addLog("error", `基础验证测试失败: ${error}`);
    showToast("测试失败");
  }
};

// 外链验证测试
const testExternalVerification = async () => {
  if (isTestingExternal.value) return;

  isTestingExternal.value = true;
  addLog("info", "开始外链验证测试");

  try {
    await mgr.value.turnstile_verify(
      CF_TURNSTILE_TYPE.KYC_SUBMIT,
      (result: any) => {
        isTestingExternal.value = false;

        if (result === false) {
          addLog("error", "外链验证失败");
          showToast("验证失败");
          return;
        }

        addLog("success", "外链验证成功", result);
        showToast("验证成功");
      },
      undefined,
      true // 使用外链模式
    );
  } catch (error) {
    isTestingExternal.value = false;
    addLog("error", `外链验证测试失败: ${error}`);
    showToast("测试失败");
  }
};

// 运行诊断
const runDiagnostic = () => {
  if (isDiagnosing.value) return;

  isDiagnosing.value = true;
  addLog("info", "开始运行诊断...");

  // 模拟诊断过程
  setTimeout(() => {
    checkSiteKey();
    testScriptLoading();
    addLog("success", "诊断完成");
    isDiagnosing.value = false;
  }, 1000);
};

// 检查 Site Key
const checkSiteKey = () => {
  const siteKey = mgr.value.getSiteKey();
  addLog("info", `当前 Site Key: ${siteKey}`);

  if (siteKey === "1x00000000000000000000AA") {
    addLog("warning", "使用测试 Site Key");
  } else if (siteKey.startsWith("0x4")) {
    addLog("success", "Site Key 格式正确");
  } else {
    addLog("error", "Site Key 格式错误");
  }
};

// 测试脚本加载
const testScriptLoading = () => {
  if (window.turnstile) {
    addLog("success", "Turnstile 脚本已加载");
    addLog("info", `可用方法: ${Object.keys(window.turnstile).join(", ")}`);
  } else {
    addLog("error", "Turnstile 脚本未加载");
  }
};

// 清除缓存
const clearCache = () => {
  try {
    localStorage.clear();
    sessionStorage.clear();
    addLog("success", "缓存已清除");
    showToast("缓存已清除");
  } catch (error) {
    addLog("error", `清除缓存失败: ${error}`);
  }
};

// Site Key 选择确认
const onSiteKeyConfirm = ({ selectedOptions }: any) => {
  externalConfig.value.siteKey = selectedOptions[0].value;
  showSiteKeyPicker.value = false;
  addLog("info", `已选择 Site Key: ${selectedOptions[0].text}`);
};

// 外观选择确认
const onAppearanceConfirm = ({ selectedOptions }: any) => {
  externalConfig.value.appearance = selectedOptions[0].value;
  showAppearancePicker.value = false;
  addLog("info", `已选择外观: ${selectedOptions[0].text}`);
};

// 预览外链 URL
const previewExternalUrl = () => {
  const params = new URLSearchParams({
    siteKey: externalConfig.value.siteKey || currentSiteKey.value,
    appearance: externalConfig.value.appearance,
    isNative: isNative.value ? "1" : "0",
    os: osType.value,
  });

  previewUrl.value = `${externalConfig.value.host}turnstile.html?${params.toString()}`;
  showUrlDialog.value = true;
  addLog("info", "预览外链 URL");
};

// 复制外链 URL
const copyExternalUrl = async () => {
  const params = new URLSearchParams({
    siteKey: externalConfig.value.siteKey || currentSiteKey.value,
    appearance: externalConfig.value.appearance,
    isNative: isNative.value ? "1" : "0",
    os: osType.value,
  });

  const url = `${externalConfig.value.host}turnstile.html?${params.toString()}`;

  try {
    await navigator.clipboard.writeText(url);
    showToast("链接已复制到剪贴板");
    addLog("success", "外链 URL 已复制");
  } catch (error) {
    addLog("error", `复制失败: ${error}`);
    showToast("复制失败");
  }
};

// 复制到剪贴板
const copyUrlToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(previewUrl.value);
    showToast("链接已复制到剪贴板");
    addLog("success", "URL 已复制到剪贴板");
  } catch (error) {
    addLog("error", `复制失败: ${error}`);
    showToast("复制失败");
  }
  showUrlDialog.value = false;
};

// 清除日志
const clearLogs = () => {
  testLogs.value = [];
  showToast("日志已清除");
};

// 导出日志
const exportLogs = () => {
  const logData = testLogs.value.map((log) => ({
    time: log.time,
    type: log.type,
    message: log.message,
    data: log.data,
  }));

  const blob = new Blob([JSON.stringify(logData, null, 2)], { type: "application/json" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `cloudflare-test-logs-${new Date().toISOString().slice(0, 10)}.json`;
  a.click();
  URL.revokeObjectURL(url);

  addLog("success", "日志已导出");
  showToast("日志已导出");
};

// 获取环境信息
const getEnvironmentInfo = () => {
  // 获取操作系统类型
  const userAgent = navigator.userAgent;
  if (userAgent.includes("Android")) {
    osType.value = "android";
  } else if (userAgent.includes("iPhone") || userAgent.includes("iPad")) {
    osType.value = "ios";
  } else {
    osType.value = "web";
  }

  // 检查是否为原生环境
  isNative.value = ALL_APP_SOURCE_CONFIG.isNative === 1;

  addLog("info", `环境信息: ${currentEnv.value}, OS: ${osType.value}, Native: ${isNative.value}`);
};

// 初始化
onMounted(() => {
  addLog("info", "Cloudflare 测试页面已加载");

  // 获取环境信息
  getEnvironmentInfo();

  // 设置默认配置
  currentSiteKey.value = mgr.value.getSiteKey();
  externalConfig.value.siteKey = currentSiteKey.value;

  addLog("info", `默认 Site Key: ${currentSiteKey.value}`);
});
</script>

<style scoped>
.cloudflare-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 20px;
}

.test-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.test-header p {
  color: #666;
  font-size: 14px;
}

.tab-content {
  padding: 20px 0;
}

.section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.test-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.turnstile-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  margin-top: 20px;
  border: 2px dashed #e5e5e5;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  font-size: 14px;
  color: #333;
}

.value.monospace {
  font-family: monospace;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.diagnostic-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.external-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.results-container {
  max-height: 400px;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.log-item {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 4px solid;
}

.log-item.success {
  background: #f0f9ff;
  border-left-color: #07c160;
}

.log-item.error {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.log-item.warning {
  background: #fffbeb;
  border-left-color: #f59e0b;
}

.log-item.info {
  background: #f8fafc;
  border-left-color: #64748b;
}

.log-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-time {
  font-size: 12px;
  color: #666;
  min-width: 80px;
}

.log-message {
  font-size: 14px;
  flex: 1;
}

.log-data {
  margin-top: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.log-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.results-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .cloudflare-test {
    padding: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .diagnostic-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .external-actions {
    flex-direction: column;
  }
}
</style>
