<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare 400 错误修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Cloudflare 400 错误修复测试</h1>
        
        <div class="info-box">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试和诊断 Cloudflare Turnstile 的 400 Bad Request 错误。</p>
            <p>常见的 400 错误原因：</p>
            <ul>
                <li>Site Key 与当前域名不匹配</li>
                <li>域名未在 Cloudflare 控制台中授权</li>
                <li>Site Key 格式错误</li>
                <li>本地开发环境使用生产 Site Key</li>
            </ul>
        </div>

        <div class="warning-box">
            <h3>⚠️ 当前环境信息</h3>
            <div id="env-info">正在检测...</div>
        </div>

        <div>
            <button onclick="checkEnvironment()">🔍 检查环境</button>
            <button onclick="testSiteKeyValidation()">🔑 测试 Site Key 验证</button>
            <button onclick="simulateCloudflareRequest()">🌐 模拟 Cloudflare 请求</button>
            <button onclick="clearLogs()">🧹 清除日志</button>
        </div>

        <div class="debug-info" id="debug-logs">
            <div>等待测试...</div>
        </div>

        <div class="info-box">
            <h3>💡 解决方案建议</h3>
            <div id="solutions">
                <p>运行测试后将显示针对性的解决方案...</p>
            </div>
        </div>
    </div>

    <script>
        function addLog(type, message, data = null) {
            const logs = document.getElementById('debug-logs');
            const time = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.marginBottom = '5px';
            div.style.color = type === 'error' ? '#dc3545' : 
                             type === 'warning' ? '#ffc107' : 
                             type === 'success' ? '#28a745' : '#17a2b8';
            div.innerHTML = `[${time}] ${message}`;
            if (data) {
                div.innerHTML += `<br>数据: ${JSON.stringify(data, null, 2)}`;
            }
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('debug-logs').innerHTML = '<div>日志已清除</div>';
        }

        function checkEnvironment() {
            addLog('info', '🔍 开始环境检查...');
            
            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            const port = window.location.port;
            const fullUrl = window.location.href;

            const envInfo = {
                hostname,
                protocol,
                port,
                fullUrl,
                userAgent: navigator.userAgent,
                isLocalDev: hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('192.168.'),
                isDevEnv: hostname.includes('.local') || hostname.includes('dev.') || hostname.includes('test.'),
                isProd: !hostname.includes('localhost') && !hostname.includes('127.0.0.1') && !hostname.includes('192.168.') && !hostname.includes('.local') && !hostname.includes('dev.') && !hostname.includes('test.')
            };

            addLog('info', '环境信息', envInfo);

            // 更新页面显示
            const envInfoDiv = document.getElementById('env-info');
            envInfoDiv.innerHTML = `
                <strong>域名:</strong> ${hostname}<br>
                <strong>协议:</strong> ${protocol}<br>
                <strong>端口:</strong> ${port || '默认'}<br>
                <strong>环境类型:</strong> ${envInfo.isLocalDev ? '本地开发' : envInfo.isDevEnv ? '开发/测试' : '生产环境'}
            `;

            // 提供建议
            updateSolutions(envInfo);
        }

        function testSiteKeyValidation() {
            addLog('info', '🔑 开始 Site Key 验证测试...');

            const testKeys = [
                { name: '测试 Site Key', key: '1x00000000000000000000AA', expected: true },
                { name: '开发 Site Key', key: '0x4AAAAAABmR_Jwyy1tet7uX', expected: true },
                { name: '无效格式', key: 'invalid-key', expected: false },
                { name: '空值', key: '', expected: false },
                { name: '对象类型', key: {}, expected: false }
            ];

            testKeys.forEach(test => {
                const isValid = validateSiteKey(test.key);
                const result = isValid === test.expected ? '✅ 通过' : '❌ 失败';
                addLog(isValid === test.expected ? 'success' : 'error', 
                      `${test.name}: ${result}`, 
                      { key: test.key, type: typeof test.key, valid: isValid });
            });
        }

        function validateSiteKey(siteKey) {
            if (!siteKey || typeof siteKey !== "string") {
                return false;
            }

            // 检查是否是测试 Site Key
            if (siteKey === "1x00000000000000000000AA") {
                return true;
            }

            // 检查是否符合正常 Site Key 格式
            const siteKeyPattern = /^0x4[A-Za-z0-9_-]{20,25}$/;
            return siteKeyPattern.test(siteKey);
        }

        function simulateCloudflareRequest() {
            addLog('info', '🌐 模拟 Cloudflare 请求...');
            
            const hostname = window.location.hostname;
            let recommendedSiteKey;

            if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('192.168.')) {
                recommendedSiteKey = '1x00000000000000000000AA';
                addLog('warning', '本地环境检测，推荐使用测试 Site Key');
            } else {
                recommendedSiteKey = '0x4AAAAAABmR_Jwyy1tet7uX';
                addLog('info', '非本地环境，使用配置的 Site Key');
            }

            // 模拟请求参数
            const requestParams = {
                sitekey: recommendedSiteKey,
                theme: 'light',
                size: 'normal',
                domain: hostname
            };

            addLog('info', '请求参数', requestParams);

            // 模拟可能的错误情况
            if (hostname === 'localhost' && !recommendedSiteKey.startsWith('1x')) {
                addLog('error', '❌ 可能的 400 错误：本地环境使用生产 Site Key');
            } else if (hostname !== 'localhost' && recommendedSiteKey.startsWith('1x')) {
                addLog('warning', '⚠️ 可能的问题：生产环境使用测试 Site Key');
            } else {
                addLog('success', '✅ 配置看起来正确');
            }
        }

        function updateSolutions(envInfo) {
            const solutionsDiv = document.getElementById('solutions');
            let solutions = '';

            if (envInfo.isLocalDev) {
                solutions = `
                    <h4>🏠 本地开发环境解决方案：</h4>
                    <ul>
                        <li>使用测试 Site Key: <code>1x00000000000000000000AA</code></li>
                        <li>或在 Cloudflare 控制台添加 <code>localhost</code> 到域名列表</li>
                        <li>确保环境变量 <code>VITE_CF_SITE_KEY_DEV</code> 设置正确</li>
                    </ul>
                `;
            } else if (envInfo.isDevEnv) {
                solutions = `
                    <h4>🧪 开发/测试环境解决方案：</h4>
                    <ul>
                        <li>在 Cloudflare 控制台添加域名 <code>${envInfo.hostname}</code></li>
                        <li>检查对应环境的 Site Key 配置</li>
                        <li>确保 Site Key 与域名匹配</li>
                    </ul>
                `;
            } else {
                solutions = `
                    <h4>🌐 生产环境解决方案：</h4>
                    <ul>
                        <li>确保域名 <code>${envInfo.hostname}</code> 已在 Cloudflare 控制台授权</li>
                        <li>检查生产环境 Site Key 是否正确</li>
                        <li>避免使用测试 Site Key</li>
                        <li>检查 DNS 和 SSL 配置</li>
                    </ul>
                `;
            }

            solutionsDiv.innerHTML = solutions;
        }

        // 页面加载时自动检查环境
        window.addEventListener('load', () => {
            addLog('info', '页面已加载，开始自动环境检查...');
            checkEnvironment();
        });
    </script>
</body>
</html>
