<template>
  <div>
    <ZActionSheet
      v-model="showWithdrawDialog"
      title="Withdrawal Accounts"
      :closeOnClickOverlay="true"
      :confirmDisabled="getConfirmDisabled"
      :showCancelButton="false"
      :confirmText="getConfirmText"
      :onConfirm="withdrawStore.handleConfirm"
    >
      <div v-if="accounts.length || isMiniChannel">
       <div class="head-tips">
        <van-icon name="warning" />
         Minimum top-out of Php 500 for first-time users .
       </div>
        <AccountCardSelect
          @select="handleSelect"
          :item="selectedAccountInfo"
          :showSelectIcon="!isMiniChannel"
        >
        </AccountCardSelect>
        <!-- 余额 -->
         <div class="balance-wrap">
          <!-- <ZIcon type="icon-qianbao" :size="20" color="" /> -->
          <img class="wallet-icon" src="@/assets/images/account/wallet.png" />
          Your Wallet Balance(₱)
          ：
          <span class="balance">{{ amountFormatThousands(globalStore.balance) }}</span>
        </div>
        <!-- 快速选择充值金额 -->
        <div v-for="item in withdrawData" :key="item.id">
          <div v-if="item.name.toLowerCase() === curRechangeMethods.toLowerCase()">
            <!-- <div class="select-title">Select an amount</div> -->
            <div class="quick-amounts">
              <div
                v-for="tag in item.tags"
                :key="item.id+tag.id"
                class="amount-item"
                :class="{ active: selectedAmount == tag }"
                @click="() => withdrawStore.setSelectedAmount(tag)"
              >
                <div class="value">{{ amountFormatThousands(tag.toLocaleString(), 0) }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 手动输入充值金额 -->
        <div
          class="amount-input"
          :class="{ disabled: !hasWithdrawList }"
          @click="withdrawStore.handleInputClick"
        >
          <IconCoin :size="24"></IconCoin>
          <input
            maxlength="6"
            type="number"
            class="input"
            :value="selectedAmount"
            @input="withdrawStore.handleCustomAmountInput"
            :placeholder="placeholder"
            :disabled="!hasWithdrawList"
          />
        </div>
        <!-- 输入校验提示 -->
        <!-- 输入校验提示 -->
        <div class="error-tips" v-if="errTip">{{ errTip }}</div>
        <div class="warn-tips" v-if="warnTip">{{ warnTip }}</div>
      </div>
      <ZNoData v-else text="Nothin to show here yet" desc="Please add Withdrawal Accounts" />
    </ZActionSheet>
  </div>
  <!-- 账户选择列表下拉 -->
  <AccountSelectDialog></AccountSelectDialog>
  <!-- 1倍流水风控 -->
  <RiskWithdrawalDialog></RiskWithdrawalDialog>
  <!-- 小程序端 金额弹窗确认 -->
  <MoneyConfirmDialog></MoneyConfirmDialog>
  <!-- 绑定手机号、支付密码 -->
  <VerifyDialogPreconditions
    v-model:showDialog="showVerifyPreconditionsDialog"
    :succCallBack="withdrawStore.handlePasswordSuccConfirm"
  >
  </VerifyDialogPreconditions>
  <VerifyDialogLoginPassword
    :hideDialogOnVaildateSuccess="false"
    v-model:showDialog="showVerifyLoginPasswordDialog"
    :succCallBack="withdrawStore.handleLoginPasswordSuccConfirm"
  </VerifyDialogLoginPassword>
  <!-- 输入密码次数过多限制 -->
  <PayPasswordLimitDialog></PayPasswordLimitDialog>
  <!-- KYC 二次提醒去验证 -->
  <KycRemindDialog></KycRemindDialog>
</template>
<script setup lang="ts">
import { useWithdrawStore } from "@/stores/withdraw";
import { useGlobalStore } from "@/stores/global";
import AccountCardSelect from "@/views/account/withdraw-account/components/AccountCardSelect.vue";
import AccountSelectDialog from "@/views/account/withdraw-account/components/AccountSelectDialog.vue";
import VerifyDialogPreconditions from "@/components/ZVerifyDialog/VerifyDialogPreconditions.vue";
import VerifyDialogLoginPassword from "@/components/ZVerifyDialog/VerifyDialogLoginPassword.vue";
import RiskWithdrawalDialog from "./RiskWithdrawalDialog.vue";
import MoneyConfirmDialog from "./MoneyConfirmDialog.vue";
import PayPasswordLimitDialog from "./PayPasswordLimitDialog.vue";
import KycRemindDialog from './KycRemindDialog.vue'
import { showToast } from "vant";
import { amountFormatThousands } from "@/utils/core/tools";

const withdrawStore = useWithdrawStore();
const globalStore = useGlobalStore();

const {
  accounts,
  showWithdrawDialog,
  showVerifyPreconditionsDialog,
  showVerifyLoginPasswordDialog,
  errTip,
  warnTip,
  curRechangeMethods,
  selectedAmount,
  withdrawData,
  selectedAccountInfo,
  isMiniChannel,
} = storeToRefs(withdrawStore);

const { balance } = storeToRefs(globalStore);

const handleSelect = () => {
  if (isMiniChannel.value) return;
  withdrawStore.showAccountListDialog = true;
};

const getConfirmText = computed(() => {
  if (isMiniChannel.value || accounts.value.length) return "Continue";
  return "Add Account";
});

const getConfirmDisabled = computed(() => {
  // 小程序端
  if (isMiniChannel.value) {
    return !(selectedAmount.value && !errTip.value);
  } else {
    // WEB 端
    if (accounts.value.length) {
      return !(selectedAmount.value && !errTip.value);
    }
    return false;
  }
});

const hasWithdrawList = computed(() => {
  return withdrawData.value.length > 0;
});

const placeholder = computed(() => {
  if (!hasWithdrawList.value) return "- -";
  return `Enter Amount ${withdrawStore.minAmountNum} - ${withdrawStore.maxAmountNum}₱`;
});

watch(
  () => withdrawStore.showWithdrawDialog,
  (newVal) => {
    if (newVal) {
    } else {
      withdrawStore.resetData();
    }
  }
);
</script>

<style lang="scss" scoped>
.head-tips{
  display: flex;
  margin-bottom: 16px ;
width: 335px;
padding: 8px 12px;
align-items: center;
color:#FF794C;
border-radius: 8px;
background:  #FFF8F6;
/* 提醒文字 */
font-family: 'Inter';
font-size: 12px;
font-style: normal;
font-weight: 400;
line-height: 18px; /* 150% */
}
.balance-wrap {
  margin-top: 28px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  color: #999;
  font-family: 'Inter';
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;

  .wallet-icon {
    width: 20px;
    height: 20px;
    margin-right: 2px;
    vertical-align: middle;
  }

  .balance {
    color: #999;
    font-family: D-DIN;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
}

.select-title {
  margin-top: 20px;
  color: #222;
  /* 弹窗标题 */
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.quick-amounts {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-top: 20px;

  .amount-item {
    background: #f4f7fd;
    color: #666;
    border-radius: 999px;
    text-align: center;
    cursor: pointer;
    position: relative;
    width: 104px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 10px;
    border: 1px solid transparent;
    color: #666;
    font-family: D-DIN;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    &.active {
      border: 1px solid #ac1140;
      background: rgba(172, 17, 64, 0.05);
    }

    .value {
      color: #666;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }
}

.amount-input {
  display: flex;
  align-items: center;
  height: 40px;
  background: #f3f6fe;
  padding: 0 16px;
  margin-top: 16px;
  border-radius: 24px;

  &.disabled {
    background: #f5f5f5;
    opacity: 0.6;
    cursor: not-allowed;
  }

  .input {
    margin-left: 8px;
    flex: 1;
    height: 100%;
    border: none;
    color: var(--Nustar, #ac1140);
    font-family: D-DIN;
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    &:focus {
      outline: none;
    }

    &::placeholder {
      color: #bbb;
      font-family: D-DIN;
      font-size: 16px;
      font-weight: 400;
      opacity: 0.8;
    }

    &:disabled {
      color: #ccc;
      cursor: not-allowed;
      pointer-events: none; // 禁用input的事件，让事件冒泡到父容器

      &::placeholder {
        opacity: 0.9;
      }
    }
  }
}

.error-tips {
  font-size: 14px;
  color: #ac1240;
  margin-top: 8px;
  font-family: D-DIN;
}
.warn-tips {
  font-size: 14px;
  color: #c0c0c0;
  margin-top: 8px;
  font-family: D-DIN;
}
</style>
<style>
/* 全局 Toast 样式 - 不使用 scoped */
.custom-toast-width {
  max-width: 320px !important;
  min-width: 200px !important;
  white-space: pre-line;
  text-align: center;
  line-height: 1.4;
  padding: 12px 16px !important;
  border-radius: 8px !important;
}
</style>
